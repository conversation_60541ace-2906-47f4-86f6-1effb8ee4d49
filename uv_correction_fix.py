#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微伏校正修正版本
提供正确的 LUT 校正函数，可以直接集成到您的应用代码中
"""

import numpy as np

# 从 calc_sensor_uv_map.py 生成的 LUT 数据
UV_LUT_X = [19426, 51059, 78886, 161315, 224188, 270587, 339759, 411886, 475809, 573398, 631120, 711089, 777964, 809663]
UV_LUT_Y = [30535, 64674, 89394, 146203, 188653, 229566, 304495, 386124, 456978, 569395, 634034, 723603, 784924, 789318]

def apply_uv_correction(uv_raw):
    """
    使用 LUT 对原始微伏值进行校正
    
    参数:
        uv_raw: 原始微伏值 (int 或 float)
    
    返回:
        校正后的微伏值 (float)
    """
    # 确保输入在 LUT 范围内
    if uv_raw < UV_LUT_X[0]:
        return UV_LUT_Y[0]  # 低于最小值时使用第一个校正值
    elif uv_raw > UV_LUT_X[-1]:
        return UV_LUT_Y[-1]  # 高于最大值时使用最后一个校正值
    else:
        # 线性插值
        return np.interp(uv_raw, UV_LUT_X, UV_LUT_Y)

def uv_to_current(uv_corrected):
    """
    从校正后的微伏值计算电流
    基于校正结果表中的数据建立的映射关系
    """
    # 这些是从 test_uvcal.xlsx 中提取的校正后数据
    corrected_uv_values = [30535, 64674, 89394, 146203, 188653, 229566, 304495, 386124, 456978, 569395, 634034, 723603, 784924, 789318]
    current_values = [3.67, 7.65, 10.94, 21.11, 30.52, 41.12, 61.10, 83.00, 102.00, 132.60, 150.50, 176.20, 198.70, 222.60]
    
    # 确保输入在范围内
    if uv_corrected < corrected_uv_values[0]:
        return current_values[0]
    elif uv_corrected > corrected_uv_values[-1]:
        return current_values[-1]
    else:
        return np.interp(uv_corrected, corrected_uv_values, current_values)

def correct_uv_and_calculate_current(uv_raw):
    """
    完整的校正流程：原始微伏 -> 校正微伏 -> 电流
    
    参数:
        uv_raw: 原始微伏值
    
    返回:
        tuple: (校正后微伏值, 计算出的电流值)
    """
    uv_corrected = apply_uv_correction(uv_raw)
    current = uv_to_current(uv_corrected)
    return uv_corrected, current

# C 语言版本的 LUT 数据（可以直接复制到 C 代码中）
def export_c_lut():
    """导出 C 语言格式的 LUT 数据"""
    print("/* 微伏校正 LUT - C 语言版本 */")
    print("#pragma once")
    print("#include <stdint.h>")
    print()
    print(f"static const uint32_t UV_LUT_SIZE = {len(UV_LUT_X)};")
    print()
    print("static const uint32_t UV_LUT_X[] = {")
    print("    " + ", ".join(str(x) for x in UV_LUT_X))
    print("};")
    print()
    print("static const uint32_t UV_LUT_Y[] = {")
    print("    " + ", ".join(str(y) for y in UV_LUT_Y))
    print("};")
    print()
    print("/* 使用示例:")
    print(" * uint32_t uv_corrected = linear_interp(uv_raw, UV_LUT_X, UV_LUT_Y, UV_LUT_SIZE);")
    print(" */")

if __name__ == "__main__":
    print("微伏校正测试")
    print("=" * 50)
    
    # 测试您日志中的数据
    test_cases = [
        524572,  # 日志中的第一个值
        573793,  # 日志中的第二个值
        475809,  # 正确的 102A 对应值
        573398,  # 正确的 132.6A 对应值
    ]
    
    print("原始微伏uV -> 校正微伏uV -> 计算电流A")
    print("-" * 50)
    
    for uv_raw in test_cases:
        uv_corrected, current = correct_uv_and_calculate_current(uv_raw)
        print(f"{uv_raw:>8} -> {uv_corrected:>8.0f} -> {current:>6.1f}A")
    
    print("\n" + "=" * 50)
    print("C 语言 LUT 数据:")
    print("=" * 50)
    export_c_lut()
