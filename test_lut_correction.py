#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试 LUT 校正功能
"""

import numpy as np

# 从生成的 LUT 中获取的数据
LUT_X = [19426, 51059, 78886, 161315, 224188, 270587, 339759, 411886, 475809, 573398, 631120, 711089, 777964, 809663]
LUT_Y = [30535, 64674, 89394, 146203, 188653, 229566, 304495, 386124, 456978, 569395, 634034, 723603, 784924, 789318]

def apply_lut_correction(uv_raw):
    """使用 LUT 进行微伏校正"""
    return np.interp(uv_raw, LUT_X, LUT_Y)

# 测试您提供的数据
test_cases = [
    (475809, 102),    # 实际正确的：102A 对应 475809 μV
    (573398, 132.6),  # 实际正确的：132.6A 对应 573398 μV
    (524572, None),   # 日志中的：524572 μV 应该校正到什么值？
    (573793, None),   # 日志中的：573793 μV 应该校正到什么值？
]

print("LUT 校正测试:")
print("原始微伏uV -> 校正后微伏uV")
print("-" * 40)

for uv_raw, expected_current in test_cases:
    uv_corrected = apply_lut_correction(uv_raw)
    print(f"{uv_raw:>8} -> {uv_corrected:>8.0f}")
    if expected_current:
        print(f"  (期望电流: {expected_current}A)")

print("\n详细分析:")
print("=" * 50)

# 分析 475809 μV 的校正
uv_raw_102A = 475809
uv_corrected_102A = apply_lut_correction(uv_raw_102A)
print(f"102A 对应的原始微伏: {uv_raw_102A} μV")
print(f"LUT 校正后的微伏: {uv_corrected_102A:.0f} μV")

# 分析 573398 μV 的校正  
uv_raw_132A = 573398
uv_corrected_132A = apply_lut_correction(uv_raw_132A)
print(f"132.6A 对应的原始微伏: {uv_raw_132A} μV")
print(f"LUT 校正后的微伏: {uv_corrected_132A:.0f} μV")

# 检查 LUT 表中是否包含这些点
print(f"\n检查 LUT 表:")
if uv_raw_102A in LUT_X:
    idx = LUT_X.index(uv_raw_102A)
    print(f"475809 μV 在 LUT_X[{idx}], 对应 LUT_Y[{idx}] = {LUT_Y[idx]}")
else:
    print("475809 μV 不在 LUT_X 中，需要插值")

if uv_raw_132A in LUT_X:
    idx = LUT_X.index(uv_raw_132A)
    print(f"573398 μV 在 LUT_X[{idx}], 对应 LUT_Y[{idx}] = {LUT_Y[idx]}")
else:
    print("573398 μV 不在 LUT_X 中，需要插值")

print(f"\n反向验证 - 从电流推算微伏:")
print("=" * 50)

# 从校正结果表中读取数据来建立电流到微伏的关系
import pandas as pd
try:
    df = pd.read_excel('test_uvcal.xlsx', sheet_name='校正结果')
    current_values = df['实际电流A'].values
    corrected_uv_values = df['校正后微伏uV'].values

    # 建立从电流到校正微伏的插值函数
    def current_to_corrected_uv(current):
        return np.interp(current, current_values, corrected_uv_values)

    # 建立从校正微伏到电流的插值函数
    def corrected_uv_to_current(uv):
        return np.interp(uv, corrected_uv_values, current_values)

    print("测试电流到微伏的转换:")
    test_currents = [102, 132.6]
    for current in test_currents:
        expected_uv = current_to_corrected_uv(current)
        print(f"{current}A -> {expected_uv:.0f} μV (校正后)")

    print("\n测试微伏到电流的转换:")
    # 测试日志中的微伏值经过校正后应该对应什么电流
    log_uv_values = [524572, 573793]
    for uv_raw in log_uv_values:
        uv_corrected = apply_lut_correction(uv_raw)
        current_calculated = corrected_uv_to_current(uv_corrected)
        print(f"{uv_raw} μV -> {uv_corrected:.0f} μV (校正) -> {current_calculated:.1f}A")

except Exception as e:
    print(f"无法读取校正结果文件: {e}")
